export interface ApplicationStatus {
    currentStatus: string;
    createdDate: string;
    lastUpdatedDate: string;
    notes: string;
}

export interface ApplicationDetails {
    education: string;
    workExperience: string;
    motivation: string;
}

export interface MentorApplicationCredentials {
    email: string;
    password: string;
    fullName: string;
    applicationStatus: ApplicationStatus;
    applicationDetails: ApplicationDetails;
    supportingDocuments: string;
}

export interface MentorApplicationTrackingTestData {
    presubmittedApplicationMentor: MentorApplicationCredentials;
}
