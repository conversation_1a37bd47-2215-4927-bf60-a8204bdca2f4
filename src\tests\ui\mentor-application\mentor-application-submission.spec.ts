import { test } from '@playwright/test';
import { LoginPage } from '../../../pages/login/login-page';
import { MentorApplicationStatusPage } from '../../../pages/mentor-application/mentor-application-status-page';
import testData from '../../../tests-data/mentor-application-submission-data.json';
import { MentorApplicationSubmissionTestData } from '../../../data-type/mentor-application-submission.type';

// Configure test to run on Chromium only
test.describe('Mentor Application Submission Tests', () => {
    test.describe.configure({ mode: 'parallel' });

    let loginPage: LoginPage;
    let mentorApplicationStatusPage: MentorApplicationStatusPage;
    const submissionData: MentorApplicationSubmissionTestData = testData as MentorApplicationSubmissionTestData;

    test('@MentorApplicationSubmission Create new application successfully test', async ({ page }) => {
        const newMentorCredentials = submissionData.newMentorForApplication;

        await test.step('Step 1: Go to home page', async () => {
            await loginPage.goToBrowser();
        });

        await test.step('Step 2: Go to login page', async () => {
            await loginPage.clickOnSignInLink();
        });

        await test.step('Step 3: Login as new mentor user from test-data', async () => {
            await loginPage.enterEmailAndPasswordToTextBox(
                newMentorCredentials.email,
                newMentorCredentials.password
            );
            await loginPage.clickOnSignInButton();
            await loginPage.verifyLoginSuccessfully();
        });

        await test.step('Step 4: Make sure it\'s on Mentor Application Status page', async () => {
            await mentorApplicationStatusPage.verifyOnMentorApplicationStatusPage();
            await mentorApplicationStatusPage.clickMentorApplicationStatusHeading();
        });

        await test.step('Step 5: Click Create Application button', async () => {
            await mentorApplicationStatusPage.clickCreateApplicationButton();
        });

        await test.step('Check result: Check fullname and email make sure match account from test-data', async () => {
            await mentorApplicationStatusPage.verifyMentorNameAndEmailOnForm(
                newMentorCredentials.fullName,
                newMentorCredentials.email
            );
        });

        await test.step('Fill education field from test-data', async () => {
            await mentorApplicationStatusPage.fillEducationField(
                newMentorCredentials.applicationData.education
            );
        });

        await test.step('Fill work experience field from test-data', async () => {
            await mentorApplicationStatusPage.fillWorkExperienceField(
                newMentorCredentials.applicationData.workExperience
            );
        });

        await test.step('Add certification from test-data', async () => {
            await mentorApplicationStatusPage.addCertification(
                newMentorCredentials.applicationData.certification
            );
        });

        await test.step('Fill motivation field from test-data', async () => {
            await mentorApplicationStatusPage.fillMotivationField(
                newMentorCredentials.applicationData.motivation
            );
        });

        await test.step('Upload supporting documents', async () => {
            await mentorApplicationStatusPage.uploadDocuments('images.jpg');
        });

        await test.step('Submit application', async () => {
            await mentorApplicationStatusPage.submitApplication();
        });

        await test.step('Verify application status is pending', async () => {
            await mentorApplicationStatusPage.verifyCurrentStatus(
                newMentorCredentials.expectedApplicationStatus.currentStatus
            );
        });

        await test.step('Verify created and updated timestamps exist', async () => {
            // Verify timestamps exist (we can't predict exact values)
            await page.getByText('Created on:').click();
            await page.getByText('Last updated:').click();
        });

        await test.step('Verify notes section matches expected value', async () => {
            await mentorApplicationStatusPage.verifyNotes(
                newMentorCredentials.expectedApplicationStatus.notes
            );
        });

        await test.step('Check application details sections', async () => {
            await mentorApplicationStatusPage.verifyApplicationDetailsField();
            
            await test.step('Verify education section', async () => {
                await mentorApplicationStatusPage.verifyEducationSection(
                    newMentorCredentials.expectedApplicationDetails.education
                );
            });
            
            await test.step('Verify work experience section', async () => {
                await mentorApplicationStatusPage.verifyWorkExperienceSection(
                    newMentorCredentials.expectedApplicationDetails.workExperience
                );
            });
            
            await test.step('Verify motivation section', async () => {
                await mentorApplicationStatusPage.verifyMotivationSection(
                    newMentorCredentials.expectedApplicationDetails.motivation
                );
            });
        });

        await test.step('Check supporting documents section', async () => {
            await mentorApplicationStatusPage.verifySupportingDocumentsField();
            await mentorApplicationStatusPage.verifySupportingDocumentsContent(
                newMentorCredentials.expectedSupportingDocuments
            );
        });
    });
});
