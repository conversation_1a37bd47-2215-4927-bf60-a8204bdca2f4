export interface ApplicationData {
    education: string;
    workExperience: string;
    motivation: string;
    certification: string;
}

export interface ExpectedApplicationStatus {
    currentStatus: string;
    notes: string;
}

export interface ExpectedApplicationDetails {
    education: string;
    workExperience: string;
    motivation: string;
}

export interface NewMentorForApplication {
    email: string;
    password: string;
    fullName: string;
    applicationData: ApplicationData;
    expectedApplicationStatus: ExpectedApplicationStatus;
    expectedApplicationDetails: ExpectedApplicationDetails;
    expectedSupportingDocuments: string;
}

export interface MentorApplicationSubmissionTestData {
    newMentorForApplication: NewMentorForApplication;
}
